package com.manaknight.app.ui.screens

import Manaknight.R
import android.app.Dialog
import android.os.Bundle
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.ui.unit.sp
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.MoreVert
import com.manaknight.app.network.Status
import com.manaknight.app.viewmodels.*
import com.manaknight.app.viewmodels.BaasViewModel
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.navigation.NavController
import com.manaknight.app.model.remote.TrackingLabourResponse
import com.manaknight.app.model.remote.TrackingMaterialResponse
import com.manaknight.app.model.remote.profitPro.DrawItem
import com.manaknight.app.model.remote.profitPro.Draws
import com.manaknight.app.model.remote.profitPro.Labor
import com.manaknight.app.model.remote.profitPro.MaterialItem
import com.manaknight.app.model.remote.profitPro.Materials
import com.manaknight.app.model.remote.profitPro.ProjectTrackingResponse
import com.manaknight.app.network.Resource
import com.manaknight.app.ui.components.CustomCheckbox
import com.manaknight.app.utils.CustomUtils.formatDate
import androidx.compose.ui.window.Dialog
import androidx.navigation.fragment.findNavController
import com.manaknight.app.extensions.snackBarForDialog
import com.manaknight.app.model.remote.profitPro.TeamMembers
import com.manaknight.app.ui.utils.isTabletLayout
import com.manaknight.app.ui.utils.getMaxContentWidth
import com.manaknight.app.ui.components.ResponsiveSheetContainer
import com.manaknight.app.utils.CustomUtils.clearCachedProjectDetails
import com.manaknight.app.extensions.snackBar



@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProjectTrackingScreen(projectId: Int, projectName:String, baasViewModel: BaasViewModel,  dialog: Dialog, navController: NavController, onSaveMaterial: (String, String) -> Unit, onEditMaterial: (String, String, Int) -> Unit, onUpdateDraws: (Int, String, DrawItem) -> Unit, onEditLabor: (Int, String) -> Unit, onSendInvoice: ( String) -> Unit, onEditInvoice: (Int, String) -> Unit) {
    LaunchedEffect(projectId) {
        baasViewModel.setTrackingProjectId(projectId)
        baasViewModel.fetchProjectTrackingData(projectId) // Ensure data is fetched when projectId changes
    }

    val trackingResponseResource by baasViewModel.trackingResponse.observeAsState(initial = Resource.loading(null))
    val projectDetailsResource by baasViewModel.projectDetailsResource.observeAsState()
    var showStatusFilter by remember { mutableStateOf(false) }
    var selectedStatus by remember { mutableStateOf("All") }
    val sheetState = rememberModalBottomSheetState()
    LaunchedEffect(trackingResponseResource?.status) {
        when (trackingResponseResource?.status) {
            Status.LOADING -> dialog.show()
            Status.SUCCESS, Status.ERROR -> dialog.hide()
            else -> {}
        }
    }

    var selectedTab by remember { mutableStateOf("Draws") }
    var showAddMaterialSheet by remember { mutableStateOf(false) }
    var showEditLaborSheet by remember { mutableStateOf(false) }
    var showAddPaymentMethodSheet by remember { mutableStateOf(false) }
    var showInvoiceOptionsSheet by remember { mutableStateOf(false) }
    var selectedDrawItem by remember { mutableStateOf<DrawItem?>(null) }
    var selectedLaborItem by remember { mutableStateOf<com.manaknight.app.model.remote.profitPro.TeamMembers?>(null) }
    var filterStatus by remember { mutableStateOf<String?>(null) } // To handle the filter
    val context = LocalContext.current


    // Access the ProjectTrackingResponse data when in the SUCCESS state
    val projectTrackingResponse = trackingResponseResource?.data


    Surface(color = Color.White) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Top Bar - Fixed at top (always full width)
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp), // typical toolbar height
                contentAlignment = Alignment.Center
            ) {
                // Back Button
                IconButton(
                    onClick = { navController.popBackStack() },
                    modifier = Modifier.align(Alignment.CenterStart)
                ) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                }

                // Title Text - always centered
                Text(
                    text = "Tracking",

                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                )

                // More Options Button
                IconButton(
                    onClick = { /* Handle more options */ },
                    modifier = Modifier.align(Alignment.CenterEnd)
                ) {
//                    Icon(
//                        painter = painterResource(id = R.drawable.ic_options_bold),
//                        contentDescription = "More",
//                        tint = colorResource(R.color.black),
//                        modifier = Modifier.size(16.dp)
//                    )
                }
            }

            // Scrollable content area - responsive adaptation
            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                Column(
                    modifier = Modifier
                        .then(
                            if (isTabletLayout()) {
                                Modifier
                                    .align(Alignment.TopCenter)
                                    .widthIn(max = getMaxContentWidth())
                            } else {
                                Modifier.fillMaxWidth()
                            }
                        )
                        .verticalScroll(rememberScrollState())
                        .padding(16.dp)
                ) {
                Spacer(modifier = Modifier.height(16.dp))

                // Project Info (You might want to observe project details here as well)
                Text(
                    text = "#$projectId • ${projectName}", // Replace with actual project name if available
                    style = MaterialTheme.typography.titleMedium
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Tabs
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    TabButton(text = "Draws", selected = selectedTab == "Draws") {
                        selectedTab = "Draws"
                    }
                    TabButton(text = "Labor", selected = selectedTab == "Labor") {
                        selectedTab = "Labor"
                    }
                    TabButton(text = "Materials", selected = selectedTab == "Materials") {
                        selectedTab = "Materials"
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

            // Observe the project tracking response
            val trackingResponseResource by baasViewModel.trackingResponse.observeAsState(
                initial = Resource.loading(
                    null
                )
            )

            // Display content based on the state of the tracking response
            when (trackingResponseResource?.status) {
                Status.LOADING -> {
                    // Show loading indicator
                    dialog.show()
                }

                Status.SUCCESS -> {
                    dialog.hide()
                    trackingResponseResource.data?.let { response ->
                        // Check for errors in the response itself (if your API returns an error flag)
                        if (response.error == true) {
                            // Handle the error case based on the response's error flag
                            Text(text = "Error: ${response.error}") // Replace with proper error UI
                        } else if (response.trackingData != null) {
                            // Access the tracking data
                            val trackingData = response.trackingData
                            // Content based on selected tab
                            when (selectedTab) {
                                "Draws" -> DrawsContent(
                                    drawData = trackingData.draws,
                                    selectedStatus = selectedStatus,
                                    showStatusFilter = { showStatusFilter = it },
                                    onShowPaymentMethodSheet = { drawItem ->
                                        selectedDrawItem = drawItem
                                        showAddPaymentMethodSheet = true
                                    },
                                    onShowInvoiceOptionsSheet = { drawItem ->
                                        selectedDrawItem = drawItem
                                        showInvoiceOptionsSheet = true
                                    })

                                "Labor" -> trackingData.labor?.let {
                                    LaborContent(
                                        laborData = it,
                                        onLaborEditClick = { showEditLaborSheet = true },
                                        onEditLabor = { id, hours ->
                                            onEditLabor(id, hours)
                                        }
                                    )
                                }
                                "Materials" -> trackingData.materials?.let {
                                    MaterialsContent(
                                        materialsData = it,
                                        onNewMaterialClick = { showAddMaterialSheet = true },
                                        onEditMaterial = { materialName: String, unitCost: String, id: Int ->
                                            onEditMaterial(
                                                materialName,
                                                unitCost,
                                                id
                                            )
                                        })
                                }
                                else -> DrawsContent(
                                    drawData = trackingData.draws,
                                    selectedStatus = selectedStatus,
                                    showStatusFilter = { showStatusFilter = it },
                                    onShowPaymentMethodSheet = { drawItem ->
                                        selectedDrawItem = drawItem
                                        showAddPaymentMethodSheet = true
                                    },
                                    onShowInvoiceOptionsSheet = { drawItem ->
                                        selectedDrawItem = drawItem
                                        showInvoiceOptionsSheet = true
                                    })
                            }
                        } else {
                            // Handle the case where data is null but status is SUCCESS (unexpected)
                            Text(text = "No tracking data available.")
                        }
                    }
                }

                Status.ERROR -> {
                    dialog.hide()
                    // Show error message
                    Text(text = "Error fetching tracking data: ${trackingResponseResource.message}")
                }

                else -> {
                    // Optional: Handle other states if needed
                    Text(text = "Idle State")
                }
            }

                // Add some bottom padding to ensure content doesn't get cut off
                Spacer(modifier = Modifier.height(100.dp))
                }
            }

        // Bottom sheets - outside of scrollable content
        if (showAddMaterialSheet) {
            var materialName by remember { mutableStateOf("") }
            var unitCost by remember { mutableStateOf("") }

            ResponsiveSheetContainer(
                showSheet = showAddMaterialSheet,
                onDismiss = { showAddMaterialSheet = false },
                sheetState = sheetState,
                headerContent = {
                    AddMaterialExpenseSheetHeader(
                        materialName = materialName,
                        unitCost = unitCost,
                        onDismiss = { showAddMaterialSheet = false },
                        onSave = { name, cost ->
                            onSaveMaterial(name, cost)
                            showAddMaterialSheet = false
                            println("Saving material: $name, unit cost: $cost")
                        }
                    )
                },
                content = {
                    AddMaterialExpenseSheetContentBody(
                        materialName = materialName,
                        onMaterialNameChange = { materialName = it },
                        unitCost = unitCost,
                        onUnitCostChange = { unitCost = it }
                    )
                }
            )
        }


            if (showStatusFilter) {
                ResponsiveSheetContainer(
                    showSheet = showStatusFilter,
                    onDismiss = { showStatusFilter = false },
                    sheetState = sheetState,
                    headerContent = {
                        DrawStatusFilterSheetHeader(
                            onDismiss = { showStatusFilter = false }
                        )
                    },
                    content = {
                        DrawStatusFilterSheetContent(
                            onStatusSelected = { status ->
                                selectedStatus = status
                                showStatusFilter = false
                            }
                        )
                    },
                    height = 300.dp
                )
            }
            if (showAddPaymentMethodSheet && selectedDrawItem != null) {
                // LIFT STATE UP
                val initialPaymentMethod = when (selectedDrawItem!!.paymentType) {
                    "0" -> 0
                    "1" -> 1
                    "2" -> 2
                    else -> -1
                }
                var selectedPaymentMethod by remember { mutableStateOf(initialPaymentMethod) }
                var checkNumber by remember { mutableStateOf(selectedDrawItem!!.checkNo ?: "") }

                val isSaveEnabled = when (selectedPaymentMethod) {
                    -1 -> false
                    1 -> checkNumber.isNotBlank()
                    0, 2 -> true
                    else -> false
                }

                ResponsiveSheetContainer(
                    showSheet = showAddPaymentMethodSheet,
                    onDismiss = {
                        showAddPaymentMethodSheet = false
                        selectedDrawItem = null
                    },
                    sheetState = sheetState,
                    headerContent = {
                        AddPaymentMethodSheetHeader(
                            selectedPaymentMethod = selectedPaymentMethod,
                            checkNumber = checkNumber,
                            isSaveEnabled = isSaveEnabled,
                            onDismiss = {
                                showAddPaymentMethodSheet = false
                                selectedDrawItem = null
                            },
                            onSave = { paymentMethodId, checkNo ->
                                selectedDrawItem?.let { drawItem ->
                                    onUpdateDraws(paymentMethodId, checkNo, drawItem)
                                    showAddPaymentMethodSheet = false
                                    selectedDrawItem = null
                                }
                            }
                        )
                    },
                    content = {
                        AddPaymentMethodSheetContent(
                            selectedPaymentMethod = selectedPaymentMethod,
                            onSelectedPaymentMethodChange = { selectedPaymentMethod = it },
                            checkNumber = checkNumber,
                            onCheckNumberChange = { checkNumber = it }
                        )
                    },
                    height = 500.dp
                )
            }

            // Invoice Options Sheet
            if (showInvoiceOptionsSheet && selectedDrawItem != null) {
                ResponsiveSheetContainer(
                    showSheet = showInvoiceOptionsSheet,
                    onDismiss = {
                        showInvoiceOptionsSheet = false
                        selectedDrawItem = null
                    },
                    sheetState = sheetState,
                    headerContent = {
                        InvoiceOptionsSheetHeader(
                            onDismiss = {
                                showInvoiceOptionsSheet = false
                                selectedDrawItem = null
                            }
                        )
                    },
                    content = {
                        InvoiceOptionsSheetContent(
                            drawItem = selectedDrawItem!!,
                            onViewInvoice = {
                                // Handle View Invoice action - Navigate to Invoice screen
                                showInvoiceOptionsSheet = false
                                val projectIdToNavigate = selectedDrawItem?.projectId ?: projectId
                                selectedDrawItem = null

                                // Navigate to Invoice screen with project ID
                                val bundle = Bundle().apply {
                                    putInt("projectID", projectIdToNavigate)
                                }
                                navController.navigate(R.id.InvoiceFragment, bundle)
                            },
                            onSendInvoice = {
                                // Handle Send Invoice action
                                showInvoiceOptionsSheet = false
                                selectedDrawItem = null

                                // Get project details to access customer email
                                // Use the existing projectDetailsResource that's already being observed
                                val currentProjectDetails = projectDetailsResource
                                when (currentProjectDetails?.status) {
                                    Status.SUCCESS -> {
                                        val customerEmail = currentProjectDetails.data?.customer?.email
                                        if (!customerEmail.isNullOrEmpty()) {
                                            onSendInvoice(customerEmail)
                                        } else {
                                            // Handle case where email is not available
                                            Log.e("ProjectTrackingScreen", "Customer email not available")
                                            // Show user feedback
                                            snackBar("Customer email not available. Please check project details.")
                                        }
                                    }
                                    Status.ERROR -> {
                                        Log.e("ProjectTrackingScreen", "Failed to get project details: ${currentProjectDetails.message}")
                                        // Show user feedback
                                        snackBar("Failed to get project details. Please try again.")
                                    }
                                    Status.LOADING -> {
                                        // Show loading if needed
                                    }
                                    null -> {
                                        // Fetch project details if not available
                                        baasViewModel.fetchProjectDetails(projectId)
                                        Log.e("ProjectTrackingScreen", "Project details not available, fetching...")
                                    }
                                }

                                baasViewModel.cacheProjectDetailsResource(null)
                                clearCachedProjectDetails(context)
                            },
                            onEdit = {
                                // Handle Send Invoice action
                                showInvoiceOptionsSheet = false
                                selectedDrawItem = null
                                // TODO: Implement send invoice logic
                            }
                        )
                    },
                    height = 250.dp
                )
            }
        }
    }



}



@Composable
fun TabButton(text: String, selected: Boolean, onClick: () -> Unit) {
    Button(
        onClick = onClick,
        shape = RoundedCornerShape(4.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor  = if (selected) colorResource(R.color.white) else Color.Transparent
        ),
        border = BorderStroke(1.dp, if (selected) colorResource(R.color.black) else Color.Transparent),
        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 2.dp), // Tight padding
        modifier = Modifier
            .padding(2.dp)
            .height(28.dp)
            .wrapContentWidth() // Shrinks button to fit content
            .defaultMinSize(minWidth = 0.dp)

    ) {
        Text(text = text, color = if (selected) Color.Black else Color.LightGray)
    }
}

@Composable
fun DrawsContent(
    drawData: Draws?,
    selectedStatus: String = "All",
    showStatusFilter: (Boolean) -> Unit,
    onShowPaymentMethodSheet: (DrawItem) -> Unit,
    onShowInvoiceOptionsSheet: (DrawItem) -> Unit
) {

    DrawsStatusFilterDropdown(selectedStatus) {
        showStatusFilter(true)
    }

    Spacer(modifier = Modifier.height(8.dp))

    when  {
        drawData == null -> {
            Text("No draws data available.")
        }
       else -> {
           val drawsList = drawData.list
           if (drawsList.isNullOrEmpty()) {
               Text("No draws available.")
           }

            if (drawsList != null) {
               Column {
                    // Find the next available draw index
                    val nextAvailableIndex = findNextAvailableDrawIndex(drawsList)
                    
                    drawsList.forEachIndexed { index, drawItem ->
                        // Check if this draw can be marked (sequential logic)
                        val canBeMarked = canMarkDraw(drawsList, index)
                        val isNextAvailable = index == nextAvailableIndex
                        
                        ListItem(
                            number = index + 1,
                            amount = drawItem.amount?.let { try { "$${String.format("%.2f", it.toDouble())}" } catch (e: NumberFormatException) { "$$it" } } ?: "N/A",
                            badge = getDrawStatusText(drawItem.paymentType?.toString()),
                            status = drawItem.status?.toString(),
                            description = drawItem.description?.toString(),
                            date = drawItem.updatedAt?.toString(),
                            onMoreOptionsClick = { onShowInvoiceOptionsSheet(drawItem) },
                            onCheckmarkClick = { 
                                if (canBeMarked) {
                                    onShowPaymentMethodSheet(drawItem)
                                }
                            },
                            isCheckboxEnabled = canBeMarked,
                            isNextAvailable = isNextAvailable
                        )
                    }
                    
                    // Add info text about sequential requirement
                    if (drawsList.size > 1) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Note: Draws must be completed in order. Complete the previous draw before marking the next one.",
                            fontSize = 12.sp,
                            color = colorResource(R.color.text_sub),
                            modifier = Modifier.padding(horizontal = 4.dp)
                        )
                    }
                }
            } else {
                Text("No draws data available.")
            }
        }


    }
}

// Helper function to determine if a draw can be marked based on sequential logic
private fun canMarkDraw(drawsList: List<DrawItem>, currentIndex: Int): Boolean {
    // First draw can always be marked
    if (currentIndex == 0) {
        return true
    }
    
    // For subsequent draws, check if all previous draws are marked
    for (i in 0 until currentIndex) {
        val previousDraw = drawsList[i]
        val isPreviousMarked = previousDraw.status?.toIntOrNull() == 1
        if (!isPreviousMarked) {
            return false
        }
    }
    
    return true
}

// Helper function to find the next available draw index
private fun findNextAvailableDrawIndex(drawsList: List<DrawItem>): Int {
    for (i in drawsList.indices) {
        val drawItem = drawsList[i]
        val isMarked = drawItem.status?.toIntOrNull() == 1
        
        if (!isMarked) {
            return i
        }
    }
    
    // If all draws are marked, return -1 (no next available)
    return -1
}

// Status Filter Sheet Header Component
@Composable
fun DrawStatusFilterSheetHeader(
    onDismiss: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Filled.Close, contentDescription = "Close")
        }
        Text(
            text = "Select Status",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color.Black
        )
        // Empty space to balance the layout
        Spacer(modifier = Modifier.width(48.dp))
    }
}

// Status Filter Sheet Content Component
@Composable
fun DrawStatusFilterSheetContent(onStatusSelected: (String) -> Unit) {
    Column(modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)) {
        StatusOption("All", onStatusSelected)
        StatusOption("Additional charge", onStatusSelected)
        StatusOption("Credits", onStatusSelected)
        StatusOption("Adjusted by credit", onStatusSelected)
    }
}

// Legacy Status Filter Sheet Composable (kept for backward compatibility if needed)
@Composable
fun DrawStatusFilterSheet(onDismiss: () -> Unit, onStatusSelected: (String) -> Unit) {
    Column(modifier = Modifier.padding(16.dp)) {
        Spacer(Modifier.height(8.dp))
        StatusOption("All", onStatusSelected)
        StatusOption("Additional charge", onStatusSelected)
        StatusOption("Credits", onStatusSelected)
        StatusOption("Adjusted by credit", onStatusSelected)
    }
}

// Status Filter Dropdown Composable
@Composable
fun DrawsStatusFilterDropdown(selectedStatus: String, onClick: () -> Unit) {


    Button(
        onClick = onClick,
        shape = RoundedCornerShape(4.dp),
        colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
        border = BorderStroke(1.dp, colorResource(R.color.stroke_soft)),
        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp), // Tight padding
        modifier = Modifier
            .padding(2.dp)
            .height(28.dp)
            .wrapContentWidth() // Shrinks button to fit content
            .defaultMinSize(minWidth = 0.dp) // Prevents enforcing min width
    ) {
        Text(
            text =  " $selectedStatus",
            fontSize = 16.sp,
            color = colorResource(R.color.text_sub)
        )
        Spacer(modifier = Modifier.width(4.dp)) // Space between text and icon
        Icon(
            painter = painterResource(id = R.drawable.ic_dropdown), // Replace with your actual icon
            tint = colorResource(R.color.text_sub),
            contentDescription = "Filter Status",
            modifier = Modifier.size(20.dp)
        )
    }



}

// Dropdown Filter Button Composable
@Composable
fun DrawsDropdownFilter(label: String) {
    Button(
        onClick = { /* Handle dropdown */ },
        colors = ButtonDefaults.buttonColors(colorResource(R.color.white)),
        shape = RoundedCornerShape(8.dp)
    ) {
        Text(text = label, color = Color.Black)
        Icon(
            painter = painterResource(id = R.drawable.ic_dropdown), // Replace with your actual icon
            contentDescription = "Add Project",
            modifier = Modifier.size(16.dp)
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LaborContent(
    laborData: Labor,
    onLaborEditClick: () -> Unit,
    onEditLabor: (Int, String) -> Unit = { _, _ -> }
) {
    var showEditSheet by remember { mutableStateOf(false) }
    var selectedItem by remember { mutableStateOf<TeamMembers?>(null) }
    val sheetState = rememberModalBottomSheetState()
    Column {
        when  {
            (laborData == null )  -> {
                Text("No labor data available.")
            }
           else -> {

                val labor = laborData

                if (labor != null) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .border(0.5.dp, Color.LightGray, RoundedCornerShape(8.dp))
                            .padding(vertical = 12.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        LaborSummaryItem(
                            label = "Labor budget",
                            percentageValue = "",
                            value = "$${labor?.laborBudget?.let { String.format("%.2f", it) } ?: "N/A"}"
                        )
                        Divider(thickness = 1.dp, color = colorResource(R.color.stroke_soft))
                        LaborSummaryItem(
                            label = "Labor spent",
                            percentageValue = "(${labor?.laborSpentPercentage?.let { try { String.format("%.0f", it.toDouble()) } catch (e: NumberFormatException) { it } } ?: "0"}%) ",
                            value = "$${labor?.laborSpent?.let { String.format("%.2f", it) } ?: "N/A"}"
                        )
                        Divider(thickness = 1.dp, color = colorResource(R.color.stroke_soft))
                        LaborSummaryItem(
                            label = "Labor balance",
                            percentageValue = "(${labor?.remainingPercentage?.let { try { String.format("%.0f", it.toDouble()) } catch (e: NumberFormatException) { it } } ?: "0"}%) ",
                            value = "$${labor?.laborBalance?.let { String.format("%.2f", it) } ?: "N/A"}"
                        )
                    }
                }
                else {
                    Text("No labor data available.")
                }

                Spacer(modifier = Modifier.height(16.dp))
                Column(modifier = Modifier
                    .fillMaxWidth()
                    .border(0.5.dp, Color.LightGray, RoundedCornerShape(8.dp))
                    .padding(vertical=12.dp)) {

                    Row(
                        modifier = Modifier.fillMaxWidth().padding(horizontal=12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Labor costs",
                            style = MaterialTheme.typography.titleMedium
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // List of labor costs
                    val laborList = labor?.teamMembers ?: emptyList()

                    // Use LazyColumn for efficient scrolling of potentially many items
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
//                            .border(0.5.dp, Color.LightGray, RoundedCornerShape(8.dp))
//                            .padding(vertical = 12.dp)
                            .heightIn(max = 410.dp) // Optional: Give it a max height if you want it to scroll independently within the section
                    ) {
                        items(laborList) { teamMember ->
                            LaborCostItem(
                                teamMember = teamMember,
                                onEditClick = {
                                    selectedItem = teamMember
                                    showEditSheet = true
                                }
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.height(16.dp))

                if (showEditSheet && selectedItem != null) {
                    var employeeName by remember { mutableStateOf(selectedItem?.name ?: "") }
                    var hours by remember { mutableStateOf(selectedItem?.hours ?: "") }
                    val hourlyRate = selectedItem?.hourlyRate ?: ""

                    ResponsiveSheetContainer(
                        showSheet = showEditSheet,
                        onDismiss = {
                            showEditSheet = false
                            selectedItem = null
                        },
                        sheetState = sheetState,
                        headerContent = {
                            EditLaborSheetHeader(
                                teamMember = selectedItem,
                                employeeName = employeeName,
                                hours = hours,
                                onDismiss = {
                                    showEditSheet = false
                                    selectedItem = null
                                },
                                onSave = { _, hoursWorked ->
                                    selectedItem?.let { teamMember ->
                                        teamMember.id?.let { id ->
                                            onEditLabor(id, hoursWorked)
                                        }
                                    }
                                    showEditSheet = false
                                    selectedItem = null
                                }
                            )
                        },
                        content = {
                            EditLaborSheetContentBody(
                                employeeName = employeeName,
                                onEmployeeNameChange = { employeeName = it },
                                hours = hours,
                                onHoursChange = { hours = it },
                                hourlyRate = hourlyRate
                            )
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun LaborSummaryItem(label: String, value: String,percentageValue:String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp, horizontal = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(text = label, fontSize = 14.sp)
        Text(text = percentageValue,  fontSize = 14.sp, color = colorResource(R.color.text_sub))
        Text(text = value, fontWeight = FontWeight.Medium, fontSize = 14.sp, color = colorResource(R.color.profit_black))
    }
}

@Composable
fun LaborCostItem(teamMember: TeamMembers, onEditClick: (TeamMembers) -> Unit) {
    val name = teamMember.name ?: "N/A"
    val rate = teamMember.hourlyRate ?: "N/A"
    val hours = teamMember.hours ?: "N/A"
    val cost = teamMember.cost ?: "N/A"

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(IntrinsicSize.Min),
        verticalAlignment = Alignment.CenterVertically
    ) {

        // Cell 1 - Name and Rate
        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight()
                .border(
                    0.5.dp,
                    color = colorResource(R.color.stroke_light)
                )
                .padding(12.dp)
                .align(Alignment.CenterVertically),
            contentAlignment = Alignment.CenterStart
        ) {
            Column {
                Text(
                    text = name,
                    color = colorResource(R.color.profit_black)
                )
                Text(
                    text = "$${rate}/hr",
                    color = colorResource(R.color.text_sub),
                    style = MaterialTheme.typography.labelSmall
                )
            }
        }
        // Cell  - Hours
        Box(
            modifier = Modifier
                .weight(2f)
                .fillMaxHeight()
                .border(
                    0.5.dp,
                    color = colorResource(R.color.stroke_light)
                )
                .padding(8.dp)
                .align(Alignment.CenterVertically),
            contentAlignment = Alignment.CenterStart
        ) {
            Column {
                Text(
                    text = "${hours}h",
                    color = colorResource(R.color.profit_black),
                    style = MaterialTheme.typography.labelSmall,
                    fontWeight = FontWeight.Medium
                )
            }
        }

        // Cell 2 - Total Amount
        Box(
            modifier = Modifier
                .weight(2f)
                .fillMaxHeight()
                .border(
                    0.5.dp,
                    color = colorResource(R.color.stroke_light)
                )
                .padding(8.dp)
                .align(Alignment.CenterVertically),
            contentAlignment = Alignment.CenterStart
        ) {
            Column {
                Text(
                    text = "$${hours.toFloat() * rate.toFloat()}",
                    color = colorResource(R.color.profit_black),
                    style = MaterialTheme.typography.labelSmall,
                    fontWeight = FontWeight.Medium
                )
            }
        }

        // Cell 3 (Icon)
        Box(
            modifier = Modifier
                .width(48.dp) // fixed width for icon cell
                .fillMaxHeight()
                .border(
                    0.5.dp,
                    color = colorResource(R.color.stroke_light)
                ),
            contentAlignment = Alignment.Center
        ) {
            IconButton(onClick = { onEditClick(teamMember) }) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_edit_icon), // Replace with your actual icon
                    tint = colorResource(R.color.text_sub),
                    contentDescription = "Edit Labor Cost",
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
fun ListItem(
    number: Int,
    amount: String,
    badge: String?,
    description: String?,
    status: String? = null,
    date: String? = null,
    onMoreOptionsClick: () -> Unit = {},
    onCheckmarkClick: () -> Unit = {},
    isCheckboxEnabled: Boolean = true,
    isNextAvailable: Boolean = false
) {
    // Check if status is 1 (checked state)
    val isChecked = status?.toIntOrNull() == 1

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .border(
                width = if (isNextAvailable && !isChecked) 1.5.dp else 0.5.dp,
                color = if (isNextAvailable && !isChecked) colorResource(R.color.profit_blue) else Color.LightGray,
                shape = RoundedCornerShape(8.dp)
            )
            .then(
                if (!isCheckboxEnabled) {
                    Modifier.alpha(0.6f)
                } else {
                    Modifier
                }
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.height(16.dp))
        CustomCheckbox(
            checked = isChecked,
            onCheckedChange = { 
                if (isCheckboxEnabled) {
                    onCheckmarkClick() 
                }
            },
            enabled = isCheckboxEnabled
        )

        Text(text = "$number. $amount", modifier = Modifier.weight(1f))
        if (description != null) {

            TrackingStatusBadge(status = status?:"", date = date,description=description)
        }

        IconButton(onClick = { onMoreOptionsClick() }) {

            Icon(
                painter = painterResource(id = R.drawable.ic_options_bold),
                contentDescription = "More",
                tint = colorResource(R.color.gray),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

// Function to map draw status codes to readable text
fun getDrawStatusText(status: String?): String? {

    return when (status) {
        "0" -> "Credit card"
        "1" -> "Check"
        "2" -> "Cash"
        "4" -> "Completed"
        "5" -> "Pending"
        "6" -> "Approved"
        "7" -> "Rejected"
        "8" -> "In Progress"
        "9" -> "On Hold"
        "10" -> "Cancelled"
        else -> status ?: null
    }
}

@Composable
fun TrackingStatusBadge(status: String, date: String?, description:String?) {
    val statusColor = when (status) {
        "Credit card" -> colorResource(id = R.color.project_blue_bg)
        "Check" -> colorResource(id = R.color.project_green_bg)
        "Cash" -> colorResource(id = R.color.project_purple_bg)
        "Outstanding" -> colorResource(id = R.color.project_purple_bg)
        "Pending" -> colorResource(id = R.color.project_blue_bg)
        "Approved" -> colorResource(id = R.color.project_green_bg)
        "Rejected" -> Color(0xFFFFEBEE) // Light red background
        "In Progress" -> colorResource(id = R.color.project_blue_bg)
        "On Hold" -> Color(0xFFFFF3E0) // Light orange background
        "Cancelled" -> Color(0xFFFFEBEE) // Light red background
        "Adjusted by credit" -> colorResource(id = R.color.project_blue_bg)
        "Credit" -> colorResource(id = R.color.project_purple_bg)
        "Additional charge" -> colorResource(id = R.color.project_purple_bg)
        else -> Color.Transparent
    }

    val statusTextColor = when (status) {
        "Credit card" -> colorResource(id = R.color.project_blue)
        "Check" -> colorResource(id = R.color.project_green)
        "Cash" -> colorResource(id = R.color.project_purple)
        "Outstanding" -> colorResource(id = R.color.project_purple)
        "Pending" -> colorResource(id = R.color.project_blue)
        "Approved" -> colorResource(id = R.color.project_green)
        "Rejected" -> Color(0xFFD32F2F) // Red text
        "In Progress" -> colorResource(id = R.color.project_blue)
        "On Hold" -> Color(0xFFFF8F00) // Orange text
        "Cancelled" -> Color(0xFFD32F2F) // Red text
        "Adjusted by credit" -> colorResource(id = R.color.project_blue)
        "Credit" -> colorResource(id = R.color.project_purple)
        "Additional charge" -> colorResource(id = R.color.project_purple)
        else -> Color.Gray
    }

    Box(
        modifier = Modifier
            .background(statusColor, shape = RoundedCornerShape(8.dp))
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Row(horizontalArrangement = Arrangement.spacedBy(2.dp)){
//            if (status.isNotEmpty()) {
//                Text(text = status, color = statusTextColor, fontSize = 12.sp)
//            }
            if (!description.isNullOrEmpty()) {
                Text(text = description, color = statusTextColor, fontSize = 12.sp)
            }
            if (date != null && status == 1.toString()  ) {
                Text(text = formatDate(date) , color = statusTextColor, fontSize = 12.sp)
            }

        }

    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MaterialsContent(
    materialsData: Materials,
    onNewMaterialClick: () -> Unit,
    onEditMaterial: (String, String, Int) -> Unit,
) {
    var showEditSheet by remember { mutableStateOf(false) }
    var selectedItem by remember { mutableStateOf<MaterialItem?>(null) }
    val sheetState = rememberModalBottomSheetState()
    Column {
        when  {

            materialsData == null -> {
                Text("No Materials data available.")
            }
            else -> {

                val materials = materialsData

                if (materials != null) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .border(0.5.dp, Color.LightGray, RoundedCornerShape(8.dp))
                            .padding(vertical = 12.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        LaborSummaryItem(
                            label = "Material budget",
                            percentageValue = "",
                            value = "$${materials?.materialBudget?.let { String.format("%.2f", it) } ?: "N/A"}"
                        )
                        Divider(thickness = 1.dp, color = colorResource(R.color.stroke_soft))
                        LaborSummaryItem(
                            label = "Material spent",
                            percentageValue = "",
                            value = "$${materials?.materialSpent?.let { String.format("%.2f", it) } ?: "N/A"}"
                        )
                        Divider(thickness = 1.dp, color = colorResource(R.color.stroke_soft))
                        LaborSummaryItem(
                            label = "Material balance",
                            percentageValue = "",
                            value = "$${materials?.materialBalance?.let { String.format("%.2f", it) } ?: "N/A"}"
                        )
                    }
                }
                else {
                    Text("No Materials data available.")
                }

                Spacer(modifier = Modifier.height(16.dp))
                Column(modifier = Modifier
                    .fillMaxWidth()
                    .border(0.5.dp, Color.LightGray, RoundedCornerShape(8.dp))
                    .padding(vertical=12.dp)) {

                    Row(
                        modifier = Modifier.fillMaxWidth().padding(horizontal=12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Materials",
                            style = MaterialTheme.typography.titleMedium
                        )


                        Button(
                            onClick = { onNewMaterialClick() },
                            shape = RoundedCornerShape(4.dp),
                            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                            border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                            contentPadding = PaddingValues(
                                horizontal = 8.dp,
                                vertical = 2.dp
                            ), // Tight padding
                            modifier = Modifier
                                .padding(2.dp)
                                .height(28.dp)
                                .wrapContentWidth() // Shrinks button to fit content
                                .defaultMinSize(minWidth = 0.dp) // Prevents enforcing min width
                        ) {
                            Text(
                                text = "New",
                                fontSize = 16.sp,
                                color = colorResource(R.color.profit_blue)
                            )
                            Spacer(modifier = Modifier.width(4.dp)) // Space between text and icon
                            Icon(
                                painter = painterResource(id = R.drawable.ic_add),
                                contentDescription = "New Material",
                                tint = colorResource(R.color.profit_blue),
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // List of materials

                    val materialsList = materials?.materials ?: emptyList()

                    // Use LazyColumn for efficient scrolling of potentially many items
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
//                            .border(0.5.dp, Color.LightGray, RoundedCornerShape(8.dp))
//                            .padding(vertical = 12.dp)
                            .heightIn(max = 410.dp) // Optional: Give it a max height if you want it to scroll independently within the section
                        // Remove verticalScroll here if you use LazyColumn, as LazyColumn is inherently scrollable
                    ) {
                        items(materialsList) { materialItem ->
                            // Your MaterialItem Composable
                            // Example: MaterialItem(materialItem, onEditMaterial)
                            MaterialItem(
                                materialItem = materialItem,
                                onEditClick = {
                                    selectedItem = materialItem
                                    showEditSheet = true
                                }
                            )
                              }
                    }

                    // Use Column instead of LazyColumn for better scrolling behavior
//                    materialsList.forEach { materialItem ->
//                        MaterialItem(
//                            materialItem = materialItem,
//                            onEditClick = {
//                                selectedItem = materialItem
//                                showEditSheet = true
//                            }
//                        )
//                    }

                }
                Spacer(modifier = Modifier.height(16.dp))

                if (showEditSheet && selectedItem != null) {
                    var materialName by remember { mutableStateOf(selectedItem?.name ?: "") }
                    var unitCost by remember { mutableStateOf(selectedItem?.cost?.toString() ?: "") }

                    ResponsiveSheetContainer(
                        showSheet = showEditSheet,
                        onDismiss = {
                            showEditSheet = false
                            selectedItem = null
                        },
                        sheetState = sheetState,
                        headerContent = {
                            EditMaterialExpenseSheetHeader(
                                materialName = materialName,
                                unitCost = unitCost,
                                materialId = selectedItem?.id ?: 0,
                                onDismiss = {
                                    showEditSheet = false
                                    selectedItem = null
                                },
                                onSave = { name, cost, id ->
                                    onEditMaterial(name, cost, id)
                                    println("Saving updated material: $name, unit cost: $cost, id: $id")
                                    showEditSheet = false
                                    selectedItem = null
                                }
                            )
                        },
                        content = {
                            EditMaterialExpenseSheetContentBody(
                                materialName = materialName,
                                onMaterialNameChange = { materialName = it },
                                unitCost = unitCost,
                                onUnitCostChange = { unitCost = it }
                            )
                        }
                    )
                }


            }

        }
    }
}

@Composable
fun MaterialItem(materialItem: MaterialItem, onEditClick: (MaterialItem) -> Unit) {
    val formattedDate = formatDate(materialItem.createdAt)
   val  date = formattedDate ?: "mm/dd/yyyy"
    val amount = materialItem?.cost ?: "N/A"

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(IntrinsicSize.Min),
        verticalAlignment = Alignment.CenterVertically
    ) {


            // Cell 1
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .border(
                        0.5.dp,
                        color = colorResource(R.color.stroke_light)
                    )
                   .padding(12.dp)
                    .align(Alignment.CenterVertically),
                contentAlignment = Alignment.CenterStart
            ) {
                Text(
                    text = "${date}" ?: "",
                    color = colorResource(R.color.profit_black)
                )
            }

            // Cell 2
            Box(
                modifier = Modifier
                    .weight(2f)
                    .fillMaxHeight()
                    .border(
                        0.5.dp,
                        color = colorResource(R.color.stroke_light)
                    )
                   .padding(8.dp)
                    .align(Alignment.CenterVertically),
                contentAlignment = Alignment.CenterStart
            ) {
                Column {
                    // Text(
                    //     text = materialItem.name ?: "Material Name",
                    //     color = colorResource(R.color.profit_black),
                    //     fontWeight = FontWeight.Medium
                    // )
                    Text(
                        text = "$${amount}",
                        color = colorResource(R.color.profit_black),
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            // Cell 2
            Box(
                modifier = Modifier
                    .weight(2f)
                    .fillMaxHeight()
                    .border(
                        0.5.dp,
                        color = colorResource(R.color.stroke_light)
                    )
                   .padding(8.dp)
                    .align(Alignment.CenterVertically),
                contentAlignment = Alignment.CenterStart
            ) {
                Column {
                     Text(
                         text = materialItem.name ?: "Material Name",
                         color = colorResource(R.color.profit_black),
                         fontWeight = FontWeight.Medium
                     )
//                    Text(
//                        text = "$${amount}",
//                        color = colorResource(R.color.profit_black),
//                        style = MaterialTheme.typography.labelSmall,
//                        fontWeight = FontWeight.Medium
//                    )
                }
            }


            // Cell 3 (Icon)
            Box(
                modifier = Modifier
                    .width(48.dp) // fixed width for icon cell
                    .fillMaxHeight()
                    .border(
                        0.5.dp,
                        color = colorResource(R.color.stroke_light)
                    ),
                contentAlignment = Alignment.Center
            ) {
                IconButton(onClick = { onEditClick(materialItem) }) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_edit_icon), // Replace with your actual icon
                        tint = colorResource(R.color.text_sub),
                        contentDescription = "Edit Material",
                        modifier = Modifier.size(20.dp)
                    )
                }
            }












    }

}

@Composable
fun AddMaterialExpenseSheet(onDismiss: () -> Unit, onSave: (String, String) -> Unit) {
    var materialName by remember { mutableStateOf("") } // State to hold the material name
    var unitCost by remember { mutableStateOf("") } // State to hold the input value
    Column(modifier = Modifier.padding(16.dp).heightIn(min = 741.dp)) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp), // Adjust height as needed
            contentAlignment = Alignment.Center
        ) {
            // Close button aligned to start
            IconButton(
                onClick = onDismiss,
                modifier = Modifier.align(Alignment.CenterStart)
            ) {
                Icon(Icons.Default.Close, contentDescription = "Close")
            }

            // Title always centered
            Text(
                text = "Add Material Expense",
                style = MaterialTheme.typography.headlineSmall
            )


            // Save button aligned to end
            Button(
                onClick = {
                    if (materialName.isNotBlank() && unitCost.isNotBlank()) {
                        onSave(materialName, unitCost)
                    }
                },
                shape = RoundedCornerShape(4.dp),
                colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
                modifier = Modifier.height(28.dp)
            ) {
                Text(
                    text = "Save",
                    fontSize = 16.sp,
                    color = colorResource(R.color.profit_blue)
                )
            }
        }


        Spacer(modifier = Modifier.height(16.dp))

        Text(text = "Material name")
        OutlinedTextField(
            value = materialName, // Connect the value to the state
            onValueChange = { newValue -> materialName = newValue }, // Update the state on value change
            modifier = Modifier.fillMaxWidth(),
            label = { Text("Enter material name") }, // Optional label
            shape = RoundedCornerShape(16.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(text = "Unit cost")
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = unitCost,
            onValueChange = { newValue -> unitCost = newValue },
            modifier = Modifier.fillMaxWidth(),
            leadingIcon = {
                Text(
                    text = "$",
                    style = MaterialTheme.typography.bodyLarge,
                    color = colorResource(R.color.text_sub)
                )
            },
            placeholder = { Text("0.00") },
            shape = RoundedCornerShape(16.dp)
        )
    }
}


@Composable
fun EditMaterialExpenseSheet(
    onDismiss: () -> Unit,
    materialItem: MaterialItem,
    onSave: (String, String, Int) -> Unit
) {
    var materialName by remember { mutableStateOf(materialItem.name ?: "") }
    var unitCost by remember { mutableStateOf(materialItem.cost.toString()) }


            Column(modifier = Modifier.padding(16.dp).heightIn(min = 741.dp)) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp), // Adjust height if needed
                    contentAlignment = Alignment.Center
                ) {
                    // Close button aligned to start
                    IconButton(
                        onClick = onDismiss,
                        modifier = Modifier.align(Alignment.CenterStart)
                    ) {
                        Icon(Icons.Default.Close, contentDescription = "Close")
                    }

                    // Title always centered
                    Text(
                        text = "Edit Material Expense",
                        style = MaterialTheme.typography.headlineSmall
                    )

                    // Save button aligned to end
                    Button(
                        onClick = {
                            if (materialName.isNotBlank() && unitCost.isNotBlank()) {
                                onSave(materialName, unitCost, materialItem.id ?: 0)
                            }
                        },
                        shape = RoundedCornerShape(4.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                        border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                        contentPadding = PaddingValues(
                            horizontal = 8.dp,
                            vertical = 2.dp
                        ),
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .padding(2.dp)
                            .height(28.dp)
                            .wrapContentWidth()
                            .defaultMinSize(minWidth = 0.dp)
                    ) {
                        Text(
                            text = "Save",
                            fontSize = 16.sp,
                            color = colorResource(R.color.profit_blue)
                        )
                    }
                }


                Spacer(modifier = Modifier.height(16.dp))

                Text(text = "Material name")
                Spacer(modifier = Modifier.height(4.dp))
                OutlinedTextField(
                    value = materialName,
                    onValueChange = { materialName = it },
                    modifier = Modifier.fillMaxWidth().padding(0.dp),
                    label = { Text("Enter material name") },
                    shape = RoundedCornerShape(16.dp),
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(text = "Unit cost")
                Spacer(modifier = Modifier.height(4.dp))
                OutlinedTextField(
                    value = unitCost,
                    onValueChange = { unitCost = it },
                    modifier = Modifier.fillMaxWidth().padding(0.dp),
                    leadingIcon = {
                        Text(
                            text = "$",
                            style = MaterialTheme.typography.bodyLarge,
                            color = colorResource(R.color.text_sub)
                        )
                    },
                    placeholder = { Text("0.00") },
                    shape = RoundedCornerShape(16.dp),
                )

                Spacer(modifier = Modifier.height(16.dp))

//                Text(text = "Date: ${materialItem.createdAt}")
//                Text(text = "Amount: ${materialItem.cost}")

                 }

}

@Composable
fun EditLaborSheet(onDismiss: () -> Unit) {

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onDismiss) {
                Icon(Icons.Default.Close, contentDescription = "Close")
            }
            Text(
                text = "Edit {employee.name} Hours",
                style = MaterialTheme.typography.headlineSmall,
                modifier = Modifier.weight(1f)
            )
            TextButton(onClick = { /* Handle save */ }) {
                Text("Save")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))
        Text(text =  "{employee.name}") // Assuming 'model' might be related to employee
        Spacer(modifier = Modifier.height(8.dp))
        Text(text = "Hours")
        Spacer(modifier = Modifier.height(8.dp))
        OutlinedTextField(
            value = "25", // You'll need to fetch and update this value
            onValueChange = { /* Handle hours change */ },
            modifier = Modifier
                .fillMaxWidth(),
            shape = RoundedCornerShape(16.dp)

        )

}

@Composable
fun InvoiceOptionsSheet(
    drawItem: DrawItem,
    onDismiss: () -> Unit,
    onViewInvoice: () -> Unit,
    onSendInvoice: () -> Unit,
    onEdit: () -> Unit,
) {
    Column(modifier = Modifier.padding(16.dp).heightIn(min = 350.dp)) {
        // Header
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            contentAlignment = Alignment.Center
        ) {
            // Close button aligned to start
            IconButton(
                onClick = onDismiss,
                modifier = Modifier.align(Alignment.CenterStart)
            ) {
                Icon(Icons.Default.Close, contentDescription = "Close")
            }

            // Title always centered
            Text(
                text = "Invoice Options",
                style = MaterialTheme.typography.headlineSmall
            )
        }

        Spacer(modifier = Modifier.height(24.dp))

        // View Invoice option
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onViewInvoice() }
                .padding(vertical = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {



            Text(
                text = "View Invoice",
                fontSize = 16.sp,
                color = Color.Black
            )
        }

        // Send Invoice option
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {  if (drawItem.status?.toIntOrNull() == 1) {

                    onEdit()
                } else {
                    onSendInvoice()
                } }
                .padding(vertical = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {

            val message = if (drawItem.status?.toIntOrNull() == 1) "Edit" else "Send Invoice"


            Text(
                text = message ,
                fontSize = 16.sp,
                color = Color.Black
            )
        }
    }
}

// Header and Content components for InvoiceOptionsSheet ResponsiveSheetContainer
@Composable
fun InvoiceOptionsSheetHeader(
    onDismiss: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Default.Close, contentDescription = "Close")
        }
        Text(
            text = "Invoice Options",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color.Black
        )
        // Empty space to balance the layout
        Spacer(modifier = Modifier.width(48.dp))
    }
}

@Composable
fun InvoiceOptionsSheetContent(
    drawItem: DrawItem,
    onViewInvoice: () -> Unit,
    onSendInvoice: () -> Unit,
    onEdit: () -> Unit
) {
    Column(modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)) {
        // View Invoice option
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onViewInvoice() }
                .padding(vertical = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "View Invoice",
                fontSize = 16.sp,
                color = Color.Black
            )
        }

        // Send Invoice option
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    if (drawItem.status?.toIntOrNull() == 1) {
                        onEdit()
                    } else {
                        onSendInvoice()
                    }
                }
                .padding(vertical = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val message = if (drawItem.status?.toIntOrNull() == 1) "Edit" else "Send Invoice"

            Text(
                text = message,
                fontSize = 16.sp,
                color = Color.Black
            )
        }
    }
}

@Composable
fun AddPaymentMethodSheetHeader(
    selectedPaymentMethod: Int,
    checkNumber: String,
    isSaveEnabled: Boolean,
    onDismiss: () -> Unit,
    onSave: (Int, String) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Default.Close, contentDescription = "Close")
        }
        Text(
            text = "Add Payment Method",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color.Black
        )
        Button(
            onClick = { onSave(selectedPaymentMethod, checkNumber) },
            enabled = isSaveEnabled,
            shape = RoundedCornerShape(4.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = if (isSaveEnabled) colorResource(R.color.white) else Color.LightGray,
                disabledContainerColor = Color.LightGray
            ),
            border = BorderStroke(
                1.dp,
                if (isSaveEnabled) colorResource(R.color.profit_blue) else Color.Gray
            ),
            contentPadding = PaddingValues(
                horizontal = 8.dp,
                vertical = 2.dp
            ),
            modifier = Modifier
                .padding(2.dp)
                .height(28.dp)
                .wrapContentWidth()
                .defaultMinSize(minWidth = 0.dp)
        ) {
            Text(
                text = "Save",
                fontSize = 16.sp,
                color = if (isSaveEnabled) colorResource(R.color.profit_blue) else Color.Gray
            )
        }
    }
}

@Composable
fun AddPaymentMethodSheetContent(
    selectedPaymentMethod: Int,
    onSelectedPaymentMethodChange: (Int) -> Unit,
    checkNumber: String,
    onCheckNumberChange: (String) -> Unit
) {
    Column(modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)) {
        // Credit card option
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onSelectedPaymentMethodChange(0) }
                .padding(vertical = 12.dp)
                .border(1.dp, colorResource(R.color.stroke_soft), RoundedCornerShape(10.dp)),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = selectedPaymentMethod == 0,
                onClick = { onSelectedPaymentMethodChange(0) },
                colors = RadioButtonDefaults.colors(
                    selectedColor = colorResource(R.color.brand_green)
                )
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                text = "Credit card",
                fontSize = 16.sp,
                color = Color.Black
            )
        }

        // Check option
        Column {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onSelectedPaymentMethodChange(1) }
                    .padding(vertical = 12.dp)
                    .border(1.dp, colorResource(R.color.stroke_soft), RoundedCornerShape(10.dp)),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = selectedPaymentMethod == 1,
                    onClick = { onSelectedPaymentMethodChange(1) },
                    colors = RadioButtonDefaults.colors(
                        selectedColor = colorResource(R.color.brand_green)
                    )
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = "Check",
                    fontSize = 16.sp,
                    color = Color.Black
                )
            }
            if (selectedPaymentMethod == 1) {
                Spacer(modifier = Modifier.height(16.dp))
                Column(Modifier.padding(start = 32.dp)) {
                    Text(
                        text = "* Check number",
                        fontSize = 14.sp,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = checkNumber,
                        onValueChange = onCheckNumberChange,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp), // Or remove `.height()` entirely
                        shape = RoundedCornerShape(8.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFF007AFF),
                            unfocusedBorderColor = Color.LightGray
                        )
                    )

                }
            }
        }

        // Cash option
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onSelectedPaymentMethodChange(2) }
                .padding(vertical = 12.dp)
                .border(1.dp, colorResource(R.color.stroke_soft), RoundedCornerShape(10.dp)),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = selectedPaymentMethod == 2,
                onClick = { onSelectedPaymentMethodChange(2) },
                colors = RadioButtonDefaults.colors(
                    selectedColor = colorResource(R.color.brand_green)
                )
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                text = "Cash",
                fontSize = 16.sp,
                color = Color.Black
            )
        }
    }
}

@Composable
fun AddMaterialExpenseSheetHeader(
    materialName: String,
    unitCost: String,
    onDismiss: () -> Unit,
    onSave: (String, String) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Default.Close, contentDescription = "Close")
        }
        Text(
            text = "Add Material Expense",
            style = MaterialTheme.typography.headlineSmall
        )


        Button(
            onClick = {
                onSave(materialName, unitCost)
            },
            shape = RoundedCornerShape(4.dp),
            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
            border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
            modifier = Modifier.height(28.dp)
        ) {
            Text("Save", fontSize = 16.sp, color = colorResource(R.color.profit_blue))
        }



    }
}

@Composable
fun AddMaterialExpenseSheetContentBody(
    materialName: String,
    onMaterialNameChange: (String) -> Unit,
    unitCost: String,
    onUnitCostChange: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(text = "Material name")
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = materialName,
            onValueChange = onMaterialNameChange,
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("Enter material name") },
            shape = RoundedCornerShape(16.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(text = "Unit cost")
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = unitCost,
            onValueChange = onUnitCostChange,
            modifier = Modifier.fillMaxWidth(),
            leadingIcon = {
                Text(
                    text = "$",
                    style = MaterialTheme.typography.bodyLarge,
                    color = colorResource(R.color.text_sub)
                )
            },
            placeholder = { Text("0.00") },
            shape = RoundedCornerShape(16.dp)
        )
    }
}

@Composable
fun EditMaterialExpenseSheetHeader(
    materialName: String,
    unitCost: String,
    materialId: Int,
    onDismiss: () -> Unit,
    onSave: (String, String, Int) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Default.Close, contentDescription = "Close")
        }
        Text(
            text = "Edit Material Expense",
            style = MaterialTheme.typography.headlineSmall
        )


        Button(
            onClick = {
                onSave(materialName, unitCost, materialId)
            },
            shape = RoundedCornerShape(4.dp),
            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
            border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
            modifier = Modifier.height(28.dp)
        ) {
            Text("Save", fontSize = 16.sp, color = colorResource(R.color.profit_blue))
        }


    }
}

@Composable
fun EditMaterialExpenseSheetContentBody(
    materialName: String,
    onMaterialNameChange: (String) -> Unit,
    unitCost: String,
    onUnitCostChange: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(text = "Material name")
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = materialName,
            onValueChange = onMaterialNameChange,
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("Enter material name") },
            shape = RoundedCornerShape(16.dp),
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(text = "Unit cost")
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = unitCost,
            onValueChange = onUnitCostChange,
            modifier = Modifier.fillMaxWidth(),
            leadingIcon = {
                Text(
                    text = "$",
                    style = MaterialTheme.typography.bodyLarge,
                    color = colorResource(R.color.text_sub)
                )
            },
            placeholder = { Text("0.00") },
            shape = RoundedCornerShape(16.dp),
        )
    }
}

@Composable
fun EditLaborSheetHeader(
    teamMember: TeamMembers?,
    employeeName: String,
    hours: String,
    onDismiss: () -> Unit,
    onSave: (String, String) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Default.Close, contentDescription = "Close")
        }
        Text(
            text = "Edit ${employeeName} Hours",
            style = MaterialTheme.typography.headlineSmall
        )

        Button(
            onClick = {
                if (employeeName.isNotBlank() && hours.isNotBlank()) {
                    onSave(employeeName, hours)
                }
            },
            shape = RoundedCornerShape(4.dp),
            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
            border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
            modifier = Modifier.height(28.dp)
        ) {
            Text("Save", fontSize = 16.sp, color = colorResource(R.color.profit_blue))
        }

    }
}

@Composable
fun EditLaborSheetContentBody(
    employeeName: String,
    onEmployeeNameChange: (String) -> Unit,
    hours: String,
    onHoursChange: (String) -> Unit,
    hourlyRate: String
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {

        Spacer(modifier = Modifier.height(16.dp))

        Text(text = "Hours")
        Spacer(modifier = Modifier.height(4.dp))
        OutlinedTextField(
            value = hours,
            onValueChange = onHoursChange,
            modifier = Modifier.fillMaxWidth(),
            placeholder = { Text("Enter hours worked") },
            shape = RoundedCornerShape(16.dp)
        )
    }
}
